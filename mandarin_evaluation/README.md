# 普通话评测系统

基于声学特征分析的普通话发音评测系统，支持声母、韵母、声调、流畅性四个维度的评测。

## 功能特点

- 🎙️ 实时录音评测
- 📊 四维度准确评分
- 🎯 错误定位和反馈
- 🖥️ 简洁直观的GUI界面
- 📈 评测结果可视化

## 评测维度

1. **声母评测**: 基于频谱特征分析
2. **韵母评测**: 共振峰频率对比
3. **声调评测**: 基频轮廓匹配
4. **流畅性评测**: 语速、停顿、连贯性分析

## 安装运行

```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python main.py
```

## 使用方法

1. 选择要朗读的文本
2. 点击录音按钮开始录音
3. 录音完成后自动进行评测
4. 查看详细的评测结果和改进建议

## 项目结构

```
mandarin_evaluation/
├── main.py              # 主程序入口
├── gui/                 # GUI界面模块
├── core/                # 核心评测算法
├── data/                # 数据文件
├── models/              # 声学模型
└── utils/               # 工具函数
```