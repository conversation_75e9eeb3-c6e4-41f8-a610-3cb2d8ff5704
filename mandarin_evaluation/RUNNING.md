# 普通话测评系统启动指南

## 后端启动

1. 安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

2. 启动Flask服务
```bash
python app.py
```

服务将在 http://localhost:5000 启动

## 前端启动

1. 安装Node.js依赖
```bash
cd frontend
npm install
```

2. 启动开发服务器
```bash
npm start
```

应用将在 http://localhost:3000 打开

## 使用说明

1. 在浏览器中访问 http://localhost:3000
2. 点击"发音测评"页面
3. 上传音频文件（MP3、WAV、M4A格式）
4. 输入对应的文本内容
5. 点击"开始测评"按钮
6. 等待处理完成，查看评估结果

## 注意事项

- 确保已安装Python 3.8+和Node.js 16+
- 首次运行需要安装所有依赖
- 如果使用Redis作为消息队列，请确保Redis服务已启动
- 标准音频文件应放在 backend/data/standard_audio 目录下