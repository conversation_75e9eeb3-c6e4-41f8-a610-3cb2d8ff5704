import React, { useState } from 'react';
import { Card, Upload, Button, Input, Space, Alert, Spin, message, Tabs } from 'antd';
import { UploadOutlined, SoundOutlined, BarChartOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import axios from 'axios';
import VisualizationPanel from '../components/VisualizationPanel';

const { TextArea } = Input;

const EvaluationPage: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [textContent, setTextContent] = useState('');
  const [uploading, setUploading] = useState(false);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);

  const handleUpload = async () => {
    if (!fileList.length) {
      message.error('请选择音频文件');
      return;
    }
    
    if (!textContent.trim()) {
      message.error('请输入文本内容');
      return;
    }

    const formData = new FormData();
    formData.append('audio', fileList[0].originFileObj!);
    formData.append('text', textContent);

    setUploading(true);
    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setTaskId(response.data.task_id);
      message.success('文件上传成功，正在处理中...');
      
      // 开始轮询结果
      pollResult(response.data.task_id);
    } catch (error) {
      message.error('上传失败');
      setUploading(false);
    }
  };

  const pollResult = async (id: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await axios.get(`/api/tasks/${id}`);
        const { status, result } = response.data;

        if (status === 'completed') {
          setResult(result);
          setUploading(false);
          clearInterval(interval);
        } else if (status === 'failed') {
          message.error('处理失败');
          setUploading(false);
          clearInterval(interval);
        }
      } catch (error) {
        console.error('轮询失败:', error);
      }
    }, 2000);
  };

  const uploadProps = {
    onRemove: (file: UploadFile) => {
      setFileList([]);
    },
    beforeUpload: (file: UploadFile) => {
      setFileList([file]);
      return false;
    },
    fileList,
    accept: '.mp3,.wav,.m4a',
  };

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Card title="发音测评" style={{ marginBottom: 24 }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <h3>1. 上传音频文件</h3>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>选择音频文件</Button>
            </Upload>
            <p style={{ color: '#666', marginTop: 8 }}>
              支持格式：MP3、WAV、M4A，最大100MB
            </p>
          </div>

          <div>
            <h3>2. 输入文本内容</h3>
            <TextArea
              rows={6}
              placeholder="请输入要测评的文本内容..."
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
            />
          </div>

          <Button
            type="primary"
            icon={<SoundOutlined />}
            loading={uploading}
            onClick={handleUpload}
            size="large"
          >
            开始测评
          </Button>
        </Space>
      </Card>

      {uploading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <p style={{ marginTop: 16 }}>正在处理中，请稍候...</p>
          </div>
        </Card>
      )}

      {result && (
        <Card>
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: '基本结果',
                children: <ResultDisplay result={result} />,
              },
              {
                key: 'visualization',
                label: (
                  <span>
                    <BarChartOutlined />
                    可视化分析
                  </span>
                ),
                children: <VisualizationPanel result={result} />,
              },
            ]}
          />
        </Card>
      )}
    </div>
  );
};

const ResultDisplay: React.FC<{ result: any }> = ({ result }) => {
  return (
    <Card title="测评结果" style={{ marginTop: 24 }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 总分 */}
        <Alert
          message={`总分：${result.overall_score}分`}
          description={
            <div style={{ fontSize: 18, fontWeight: 'bold' }}>
              {result.overall_score >= 90 && '优秀'}
              {result.overall_score >= 80 && result.overall_score < 90 && '良好'}
              {result.overall_score >= 70 && result.overall_score < 80 && '中等'}
              {result.overall_score >= 60 && result.overall_score < 70 && '及格'}
              {result.overall_score < 60 && '需改进'}
            </div>
          }
          type={result.overall_score >= 80 ? 'success' : result.overall_score >= 60 ? 'warning' : 'error'}
          showIcon
        />

        {/* 各维度分数 */}
        <Card size="small" title="各维度得分">
          <div style={{ display: 'flex', justifyContent: 'space-around' }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                {result.dimension_scores.initial}
              </div>
              <div>声母</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                {result.dimension_scores.final}
              </div>
              <div>韵母</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa8c16' }}>
                {result.dimension_scores.tone}
              </div>
              <div>声调</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                {result.dimension_scores.fluency}
              </div>
              <div>流畅性</div>
            </div>
          </div>
        </Card>

        {/* 错误文本展示 */}
        <Card size="small" title="错误分析">
          <div style={{ lineHeight: 2, fontSize: 16 }}>
            {result.text_with_errors.map((item: any, index: number) => (
              <span
                key={index}
                style={{
                  backgroundColor: item.error_type ? getColorByErrorType(item.error_type) : 'transparent',
                  padding: '2px 4px',
                  borderRadius: '3px',
                  position: 'relative',
                }}
                title={item.error_detail}
              >
                {item.char}
              </span>
            ))}
          </div>
          <div style={{ marginTop: 16, fontSize: 14, color: '#666' }}>
            <div style={{ marginBottom: 8 }}>图例：</div>
            <div>
              <span style={{ backgroundColor: '#ff4d4f', color: 'white', padding: '2px 4px', borderRadius: '3px', marginRight: 8 }}>
                声母错误
              </span>
              <span style={{ backgroundColor: '#fa8c16', color: 'white', padding: '2px 4px', borderRadius: '3px', marginRight: 8 }}>
                韵母错误
              </span>
              <span style={{ backgroundColor: '#1890ff', color: 'white', padding: '2px 4px', borderRadius: '3px', marginRight: 8 }}>
                声调错误
              </span>
              <span style={{ backgroundColor: '#8c8c8c', color: 'white', padding: '2px 4px', borderRadius: '3px' }}>
                其他错误
              </span>
            </div>
          </div>
        </Card>

        {/* 统计信息 */}
        <Card size="small" title="统计信息">
          <div>
            <p>音频时长：{result.audio_analysis.duration.toFixed(2)}秒</p>
            <p>语速：{result.audio_analysis.speech_rate.toFixed(0)}字/分钟</p>
            <p>错误总数：{result.error_summary.total_errors}个</p>
            <p>准确率：{result.error_summary.accuracy}%</p>
          </div>
        </Card>
      </Space>
    </Card>
  );
};

const getColorByErrorType = (errorType: string) => {
  switch (errorType) {
    case 'initial':
      return '#ff4d4f';
    case 'final':
      return '#fa8c16';
    case 'tone':
      return '#1890ff';
    default:
      return '#8c8c8c';
  }
};

export default EvaluationPage;