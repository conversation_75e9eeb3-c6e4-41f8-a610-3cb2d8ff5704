import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Button, Space, message } from 'antd';
import { PlayCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';
import type { ColumnsType } from 'antd/es/table';

interface HistoryItem {
  id: number;
  task_id: string;
  filename: string;
  status: string;
  progress: number;
  created_at: string;
  result?: any;
}

const HistoryPage: React.FC = () => {
  const [data, setData] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHistory();
  }, []);

  const fetchHistory = async () => {
    try {
      // 这里应该从后端获取历史记录
      // 暂时使用模拟数据
      const mockData: HistoryItem[] = [
        {
          id: 1,
          task_id: 'task-1',
          filename: 'test1.mp3',
          status: 'completed',
          progress: 100,
          created_at: '2024-01-01 10:00:00',
          result: {
            overall_score: 85.5,
            dimension_scores: {
              initial: 88,
              final: 82,
              tone: 86,
              fluency: 90,
            },
          },
        },
        {
          id: 2,
          task_id: 'task-2',
          filename: 'test2.mp3',
          status: 'completed',
          progress: 100,
          created_at: '2024-01-02 10:00:00',
          result: {
            overall_score: 92.0,
            dimension_scores: {
              initial: 95,
              final: 90,
              tone: 91,
              fluency: 88,
            },
          },
        },
      ];
      setData(mockData);
      setLoading(false);
    } catch (error) {
      message.error('获取历史记录失败');
      setLoading(false);
    }
  };

  const handlePlay = (record: HistoryItem) => {
    // 播放音频
    message.info('播放音频功能待实现');
  };

  const handleDelete = async (id: number) => {
    try {
      // 删除记录
      message.success('删除成功');
      fetchHistory();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const columns: ColumnsType<HistoryItem> = [
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === 'completed' ? 'green' : status === 'failed' ? 'red' : 'blue';
        const text = status === 'completed' ? '已完成' : status === 'failed' ? '失败' : '处理中';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => `${progress}%`,
    },
    {
      title: '得分',
      key: 'score',
      render: (_, record) => {
        if (record.result) {
          const score = record.result.overall_score;
          let color = 'default';
          if (score >= 90) color = 'success';
          else if (score >= 80) color = 'processing';
          else if (score >= 60) color = 'warning';
          else color = 'error';
          
          return <Tag color={color}>{score}分</Tag>;
        }
        return '-';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<PlayCircleOutlined />}
            onClick={() => handlePlay(record)}
          >
            播放
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="历史记录">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
    </Card>
  );
};

export default HistoryPage;