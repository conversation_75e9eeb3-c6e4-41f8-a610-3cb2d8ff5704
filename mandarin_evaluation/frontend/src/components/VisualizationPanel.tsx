import React from 'react';
import { Card, Row, Col, Progress, Radio } from 'antd';
import ReactECharts from 'echarts-for-react';
import type { RadioChangeEvent } from 'antd';

interface VisualizationProps {
  result: {
    overall_score: number;
    dimension_scores: {
      initial: number;
      final: number;
      tone: number;
      fluency: number;
    };
    text_with_errors: Array<{
      char: string;
      error_type?: string;
      error_detail?: string;
    }>;
    audio_analysis: {
      waveform_data: number[];
      pitch_curve: number[];
      duration: number;
      speech_rate: number;
    };
    error_summary: {
      total_errors: number;
      initial_errors: number;
      final_errors: number;
      tone_errors: number;
      accuracy: number;
    };
  };
}

const VisualizationPanel: React.FC<VisualizationProps> = ({ result }) => {
  const [chartType, setChartType] = useState('radar');

  const getRadarOption = () => {
    return {
      title: {
        text: '多维度评分',
        left: 'center',
      },
      radar: {
        indicator: [
          { name: '声母', max: 100 },
          { name: '韵母', max: 100 },
          { name: '声调', max: 100 },
          { name: '流畅性', max: 100 },
        ],
        shape: 'polygon',
        splitNumber: 4,
        axisName: {
          color: '#333',
        },
      },
      series: [
        {
          name: '得分',
          type: 'radar',
          data: [
            {
              value: [
                result.dimension_scores.initial,
                result.dimension_scores.final,
                result.dimension_scores.tone,
                result.dimension_scores.fluency,
              ],
              name: '得分',
              areaStyle: {
                color: 'rgba(24, 144, 255, 0.3)',
              },
              lineStyle: {
                color: '#1890ff',
                width: 2,
              },
            },
          ],
        },
      ],
    };
  };

  const getBarOption = () => {
    return {
      title: {
        text: '各维度得分',
        left: 'center',
      },
      xAxis: {
        type: 'category',
        data: ['声母', '韵母', '声调', '流畅性'],
      },
      yAxis: {
        type: 'value',
        max: 100,
      },
      series: [
        {
          data: [
            result.dimension_scores.initial,
            result.dimension_scores.final,
            result.dimension_scores.tone,
            result.dimension_scores.fluency,
          ],
          type: 'bar',
          itemStyle: {
            color: function (params: any) {
              const colors = ['#ff4d4f', '#fa8c16', '#1890ff', '#722ed1'];
              return colors[params.dataIndex];
            },
          },
        },
      ],
    };
  };

  const getWaveformOption = () => {
    const data = result.audio_analysis.waveform_data.map((value, index) => [index, value]);
    
    return {
      title: {
        text: '音频波形',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'value',
        name: '时间',
        show: false,
      },
      yAxis: {
        type: 'value',
        name: '振幅',
        show: false,
      },
      series: [
        {
          data: data,
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 1,
            color: '#1890ff',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(24, 144, 255, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(24, 144, 255, 0.1)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  const getPitchOption = () => {
    const data = result.audio_analysis.pitch_curve
      .map((value, index) => (value > 0 ? [index, value] : null))
      .filter((item): item is [number, number] => item !== null);
    
    return {
      title: {
        text: '基频曲线',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'value',
        name: '时间',
        show: false,
      },
      yAxis: {
        type: 'value',
        name: '频率(Hz)',
      },
      series: [
        {
          data: data,
          type: 'line',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 2,
            color: '#52c41a',
          },
        },
      ],
    };
  };

  const getErrorPieOption = () => {
    return {
      title: {
        text: '错误分布',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '错误类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: result.error_summary.initial_errors,
              name: '声母错误',
              itemStyle: { color: '#ff4d4f' },
            },
            {
              value: result.error_summary.final_errors,
              name: '韵母错误',
              itemStyle: { color: '#fa8c16' },
            },
            {
              value: result.error_summary.tone_errors,
              name: '声调错误',
              itemStyle: { color: '#1890ff' },
            },
          ],
        },
      ],
    };
  };

  const handleChartTypeChange = (e: RadioChangeEvent) => {
    setChartType(e.target.value);
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* 总分展示 */}
        <Col span={24}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 48, fontWeight: 'bold', color: '#1890ff' }}>
                {result.overall_score}
              </div>
              <div style={{ fontSize: 20, color: '#666' }}>总分</div>
              <Progress
                percent={result.overall_score}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                strokeWidth={20}
                showInfo={false}
                style={{ marginTop: 16, maxWidth: 400, margin: '16px auto 0' }}
              />
            </div>
          </Card>
        </Col>

        {/* 各维度得分 */}
        <Col span={24}>
          <Card title="各维度得分">
            <Row gutter={16}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={result.dimension_scores.initial}
                    strokeColor="#ff4d4f"
                    format={(percent) => `${percent}`}
                  />
                  <div style={{ marginTop: 8 }}>声母</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={result.dimension_scores.final}
                    strokeColor="#fa8c16"
                    format={(percent) => `${percent}`}
                  />
                  <div style={{ marginTop: 8 }}>韵母</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={result.dimension_scores.tone}
                    strokeColor="#1890ff"
                    format={(percent) => `${percent}`}
                  />
                  <div style={{ marginTop: 8 }}>声调</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={result.dimension_scores.fluency}
                    strokeColor="#722ed1"
                    format={(percent) => `${percent}`}
                  />
                  <div style={{ marginTop: 8 }}>流畅性</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 图表展示 */}
        <Col span={12}>
          <Card
            title="多维度分析"
            extra={
              <Radio.Group value={chartType} onChange={handleChartTypeChange} size="small">
                <Radio.Button value="radar">雷达图</Radio.Button>
                <Radio.Button value="bar">柱状图</Radio.Button>
              </Radio.Group>
            }
          >
            <ReactECharts
              option={chartType === 'radar' ? getRadarOption() : getBarOption()}
              style={{ height: 300 }}
            />
          </Card>
        </Col>

        <Col span={12}>
          <Card title="错误分布">
            <ReactECharts option={getErrorPieOption()} style={{ height: 300 }} />
          </Card>
        </Col>

        {/* 音频分析 */}
        <Col span={12}>
          <Card title="音频波形">
            <ReactECharts option={getWaveformOption()} style={{ height: 200 }} />
          </Card>
        </Col>

        <Col span={12}>
          <Card title="基频曲线">
            <ReactECharts option={getPitchOption()} style={{ height: 200 }} />
          </Card>
        </Col>

        {/* 统计信息 */}
        <Col span={24}>
          <Card title="统计信息">
            <Row gutter={16}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold' }}>
                    {result.audio_analysis.duration.toFixed(2)}s
                  </div>
                  <div>音频时长</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold' }}>
                    {result.audio_analysis.speech_rate.toFixed(0)}
                  </div>
                  <div>语速(字/分钟)</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold' }}>
                    {result.error_summary.total_errors}
                  </div>
                  <div>错误总数</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold' }}>
                    {result.error_summary.accuracy}%
                  </div>
                  <div>准确率</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default VisualizationPanel;