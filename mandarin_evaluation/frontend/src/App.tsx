import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import MainLayout from './components/Layout/MainLayout';
import EvaluationPage from './pages/EvaluationPage';
import HistoryPage from './pages/HistoryPage';
import './App.css';

const { Header, Content } = Layout;

const App: React.FC = () => {
  return (
    <Layout className="app-layout">
      <Header className="app-header">
        <h1 style={{ color: 'white', margin: 0 }}>普通话发音测评系统</h1>
      </Header>
      <Content className="app-content">
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route index element={<EvaluationPage />} />
            <Route path="history" element={<HistoryPage />} />
          </Route>
        </Routes>
      </Content>
    </Layout>
  );
};

export default App;