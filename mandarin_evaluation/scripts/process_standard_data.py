"""
数据处理脚本
用于整理50篇标准音频和文本数据
"""
import os
import json
import shutil
from app import create_app, db
from app.models import StandardAudio
from pypinyin import pinyin, Style
import librosa

def process_standard_data():
    """处理标准数据"""
    app = create_app()
    
    with app.app_context():
        # 确保目录存在
        audio_source_dir = "E:\\Code\\Outsourcing\\普通话测评\\音频"
        text_file = "E:\\Code\\Outsourcing\\普通话测评\\parsed_texts.json"
        standard_audio_dir = app.config['STANDARD_AUDIO_FOLDER']
        
        # 读取文本数据
        with open(text_file, 'r', encoding='utf-8') as f:
            texts_data = json.load(f)
        
        # 处理每个音频文件
        for audio_id in range(1, 51):
            audio_id_str = str(audio_id)
            
            # 查找对应的音频文件
            audio_file = None
            for ext in ['.mp3', '.wav', '.m4a']:
                possible_file = os.path.join(audio_source_dir, f"{audio_id_str}{ext}")
                if os.path.exists(possible_file):
                    audio_file = possible_file
                    break
            
            if not audio_file:
                print(f"警告: 找不到音频文件 {audio_id_str}")
                continue
            
            # 获取文本内容
            text_content = texts_data.get(audio_id_str, "")
            if not text_content:
                print(f"警告: 找不到文本内容 {audio_id_str}")
                continue
            
            # 复制音频文件到标准目录
            filename = f"{audio_id_str}{os.path.splitext(audio_file)[1]}"
            dest_path = os.path.join(standard_audio_dir, filename)
            shutil.copy2(audio_file, dest_path)
            
            # 获取音频信息
            try:
                audio_info = librosa.get_info(filename=dest_path)
                duration = audio_info['duration']
                sample_rate = audio_info['sample_rate']
            except:
                duration = 0
                sample_rate = 0
            
            # 保存到数据库
            standard_audio = StandardAudio(
                audio_id=audio_id_str,
                filename=filename,
                text_content=text_content,
                duration=duration,
                sample_rate=sample_rate
            )
            
            db.session.add(standard_audio)
            print(f"已处理: {audio_id_str}")
        
        # 提交数据库
        db.session.commit()
        print("数据处理完成!")

if __name__ == '__main__':
    process_standard_data()