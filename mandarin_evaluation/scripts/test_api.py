"""
测试脚本
用于验证系统功能
"""
import requests
import json
import os

def test_api():
    """测试API接口"""
    base_url = "http://localhost:5000/api"
    
    # 测试上传接口
    print("测试文件上传...")
    
    # 准备测试文件
    test_audio_path = "E:/Code/Outsourcing/普通话测评/音频/1.mp3"
    if not os.path.exists(test_audio_path):
        print(f"测试音频文件不存在: {test_audio_path}")
        return
    
    test_text = "这是一段测试文本，用于验证系统功能。"
    
    # 发送请求
    with open(test_audio_path, 'rb') as f:
        files = {'audio': f}
        data = {'text': test_text}
        
        try:
            response = requests.post(f"{base_url}/upload", files=files, data=data)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.json()}")
            
            if response.status_code == 200:
                task_id = response.json()['task_id']
                print(f"任务ID: {task_id}")
                
                # 轮询任务状态
                print("\n轮询任务状态...")
                import time
                for i in range(10):
                    time.sleep(2)
                    status_response = requests.get(f"{base_url}/tasks/{task_id}")
                    status_data = status_response.json()
                    print(f"状态: {status_data['status']}, 进度: {status_data['progress']}%")
                    
                    if status_data['status'] == 'completed':
                        print("任务完成！")
                        print(f"结果: {json.dumps(status_data['result'], indent=2, ensure_ascii=False)}")
                        break
                    elif status_data['status'] == 'failed':
                        print("任务失败！")
                        print(f"错误信息: {status_data['error_message']}")
                        break
                        
        except Exception as e:
            print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    test_api()