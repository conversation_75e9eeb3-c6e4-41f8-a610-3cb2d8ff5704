import os
from datetime import timedelta

class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS = {'mp3', 'wav', 'm4a'}
    
    # 数据目录
    DATA_FOLDER = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'data')
    STANDARD_AUDIO_FOLDER = os.path.join(DATA_FOLDER, 'standard_audio')
    STANDARD_TEXTS_FOLDER = os.path.join(DATA_FOLDER, 'standard_texts')
    MODELS_FOLDER = os.path.join(DATA_FOLDER, 'models')
    
    # Celery配置
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL') or 'redis://localhost:6379/0'
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND') or 'redis://localhost:6379/0'
    
    # 跨域配置
    CORS_ORIGINS = ['http://localhost:3000']
    
    # 评估配置
    SAMPLE_RATE = 16000
    N_MFCC = 13
    HOP_LENGTH = 512
    N_FFT = 2048