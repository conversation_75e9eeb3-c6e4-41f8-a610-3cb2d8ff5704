import librosa
import numpy as np
import soundfile as sf
from pydub import AudioSegment
import os

def load_audio(file_path, sr=16000):
    """
    加载音频文件
    
    Args:
        file_path: 音频文件路径
        sr: 目标采样率
        
    Returns:
        audio: 音频数据
        sr: 实际采样率
    """
    try:
        # 根据文件扩展名选择加载方式
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.mp3':
            # 使用pydub加载MP3
            audio = AudioSegment.from_mp3(file_path)
            audio = audio.set_frame_rate(sr)
            audio = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                audio = audio.reshape(-1, 2).mean(axis=1)  # 转为单声道
            audio = audio.astype(np.float32) / 32768.0  # 归一化
        else:
            # 使用librosa加载其他格式
            audio, loaded_sr = librosa.load(file_path, sr=sr)
            if loaded_sr != sr:
                audio = librosa.resample(audio, orig_sr=loaded_sr, target_sr=sr)
        
        return audio, sr
        
    except Exception as e:
        raise Exception(f"加载音频文件失败: {str(e)}")

def extract_mfcc(audio, sr, n_mfcc=13, hop_length=512):
    """
    提取MFCC特征
    
    Args:
        audio: 音频数据
        sr: 采样率
        n_mfcc: MFCC系数个数
        hop_length: 帧移
        
    Returns:
        mfcc: MFCC特征
    """
    mfcc = librosa.feature.mfcc(
        y=audio,
        sr=sr,
        n_mfcc=n_mfcc,
        hop_length=hop_length,
        n_fft=2048
    )
    return mfcc

def extract_pitch(audio, sr, fmin=80, fmax=400):
    """
    提取基频(F0)
    
    Args:
        audio: 音频数据
        sr: 采样率
        fmin: 最低频率
        fmax: 最高频率
        
    Returns:
        f0: 基频序列
        voiced_flag: 是否有声
    """
    f0, voiced_flag, voiced_probs = librosa.pyin(
        audio,
        fmin=fmin,
        fmax=fmax,
        sr=sr,
        frame_length=2048,
        hop_length=512
    )
    
    # 平滑处理
    f0 = smooth_pitch(f0, voiced_flag)
    
    return f0, voiced_flag

def smooth_pitch(f0, voiced_flag, window_size=5):
    """
    平滑基频曲线
    
    Args:
        f0: 基频序列
        voiced_flag: 是否有声
        window_size: 平滑窗口大小
        
    Returns:
        smoothed_f0: 平滑后的基频
    """
    smoothed_f0 = f0.copy()
    
    for i in range(len(f0)):
        if voiced_flag[i]:
            # 计算窗口内的有效值
            start = max(0, i - window_size // 2)
            end = min(len(f0), i + window_size // 2 + 1)
            
            window_values = f0[start:end]
            window_voiced = voiced_flag[start:end]
            
            if np.sum(window_voiced) > 0:
                # 使用中值滤波
                valid_values = window_values[window_voiced]
                smoothed_f0[i] = np.median(valid_values)
    
    return smoothed_f0

def extract_formants(audio, sr, n_formants=4):
    """
    提取共振峰
    
    Args:
        audio: 音频数据
        sr: 采样率
        n_formants: 共振峰数量
        
    Returns:
        formants: 共振峰频率列表
    """
    # 使用线性预测编码提取共振峰
    formants = []
    
    # 分帧处理
    frame_length = 2048
    hop_length = 512
    
    for i in range(0, len(audio) - frame_length, hop_length):
        frame = audio[i:i + frame_length]
        
        # 预加重
        frame = np.append(frame[0], frame[1:] - 0.97 * frame[:-1])
        
        # 加窗
        window = np.hamming(len(frame))
        frame = frame * window
        
        # LPC
        lpc_order = 2 + int(sr / 1000)  # 根据采样率确定LPC阶数
        try:
            A = librosa.lpc(frame, order=lpc_order)
            roots = np.roots(A)
            
            # 只保留单位圆内的根
            roots = roots[np.abs(roots) < 1]
            
            # 转换为频率
            angles = np.angle(roots)
            freqs = sorted(angles * (sr / (2 * np.pi)))
            
            # 只保留正频率
            freqs = [f for f in freqs if 0 < f < sr / 2]
            
            if len(freqs) >= n_formants:
                formants.append(freqs[:n_formants])
            else:
                formants.append(freqs + [0] * (n_formants - len(freqs)))
                
        except:
            formants.append([0] * n_formants)
    
    return np.array(formants)

def detect_silence(audio, sr, top_db=20, frame_length=2048, hop_length=512):
    """
    检测静音段
    
    Args:
        audio: 音频数据
        sr: 采样率
        top_db: 静音阈值(dB)
        frame_length: 帧长
        hop_length: 帧移
        
    Returns:
        intervals: 静音段列表[(start, end), ...]
    """
    # 计算RMS能量
    rms = librosa.feature.rms(
        y=audio,
        frame_length=frame_length,
        hop_length=hop_length
    )[0]
    
    # 转换为dB
    rms_db = librosa.amplitude_to_db(rms, ref=1.0)
    
    # 找到静音段
    threshold = np.max(rms_db) - top_db
    silent_frames = rms_db < threshold
    
    # 转换为时间间隔
    intervals = []
    start = None
    
    for i, is_silent in enumerate(silent_frames):
        time = i * hop_length / sr
        
        if is_silent and start is None:
            start = time
        elif not is_silent and start is not None:
            intervals.append((start, time))
            start = None
    
    # 处理最后的静音段
    if start is not None:
        intervals.append((start, len(audio) / sr))
    
    return intervals

def calculate_speech_rate(char_count, duration, pause_duration=0):
    """
    计算语速
    
    Args:
        char_count: 字符数
        duration: 总时长(秒)
        pause_duration: 停顿时长(秒)
        
    Returns:
        speech_rate: 语速(字/分钟)
    """
    effective_duration = duration - pause_duration
    if effective_duration > 0:
        return (char_count / effective_duration) * 60
    else:
        return 0

def normalize_audio(audio, target_db=-20):
    """
    音频归一化
    
    Args:
        audio: 音频数据
        target_db: 目标dB值
        
    Returns:
        normalized_audio: 归一化后的音频
    """
    # 计算RMS
    rms = np.sqrt(np.mean(audio ** 2))
    
    # 避免除零
    if rms < 1e-8:
        return audio
    
    # 计算缩放因子
    target_rms = 10 ** (target_db / 20)
    scale = target_rms / rms
    
    # 限制缩放因子，避免过度放大
    scale = min(scale, 1.0 / np.max(np.abs(audio)))
    
    return audio * scale