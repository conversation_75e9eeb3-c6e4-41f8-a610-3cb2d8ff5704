import numpy as np
import librosa
from dtw import dtw
from pypinyin import pinyin, Style
import jieba

class ForceAligner:
    """强制对齐服务"""
    
    def __init__(self, sample_rate=16000):
        self.sample_rate = sample_rate
        self.hop_length = 512
        self.n_fft = 2048
    
    def align_audio_text(self, audio, text, asr_result=None):
        """
        将音频与文本强制对齐
        
        Args:
            audio: 音频数据
            text: 文本内容
            asr_result: ASR识别结果（可选）
            
        Returns:
            list: 对齐结果
        """
        # 预处理文本
        text_info = self._preprocess_text(text)
        
        # 提取音频特征
        audio_features = self._extract_audio_features(audio)
        
        # 如果有ASR结果，使用ASR辅助对齐
        if asr_result:
            alignment = self._align_with_asr(text_info, audio_features, asr_result)
        else:
            # 使用DTW直接对齐
            alignment = self._align_with_dtw(text_info, audio_features)
        
        # 后处理：优化时间边界
        alignment = self._refine_alignment(alignment, audio)
        
        return alignment
    
    def _preprocess_text(self, text):
        """预处理文本"""
        # 分词
        words = jieba.lcut(text)
        
        # 获取字符列表
        chars = list(text.replace(' ', ''))
        
        # 转换为拼音
        pinyin_list = pinyin(text, style=Style.TONE3)
        pinyin_flat = [p[0] for p in pinyin_list if p[0]]
        
        # 分离声母韵母
        phonemes = []
        for py in pinyin_flat:
            if py and any(c.isdigit() for c in py):  # 带声调
                phoneme_info = self._separate_initial_final(py)
                phonemes.append(phoneme_info)
            else:
                phonemes.append({
                    'initial': '',
                    'final': py,
                    'tone': 0,
                    'pinyin': py
                })
        
        return {
            'text': text,
            'words': words,
            'chars': chars,
            'pinyin': pinyin_flat,
            'phonemes': phonemes
        }
    
    def _separate_initial_final(self, pinyin_with_tone):
        """分离声母韵母"""
        # 去除声调数字
        pinyin_base = ''.join([c for c in pinyin_with_tone if not c.isdigit()])
        tone = int([c for c in pinyin_with_tone if c.isdigit()][0]) if any(c.isdigit() for c in pinyin_with_tone) else 0
        
        # 声母列表
        initials = ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 
                    'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's', 'y', 'w']
        
        # 查找声母
        initial = ''
        final = pinyin_base
        
        for i in range(len(pinyin_base)):
            if pinyin_base[:i+1] in initials:
                initial = pinyin_base[:i+1]
                final = pinyin_base[i+1:]
        
        return {
            'initial': initial,
            'final': final,
            'tone': tone,
            'pinyin': pinyin_with_tone
        }
    
    def _extract_audio_features(self, audio):
        """提取音频特征"""
        features = {}
        
        # MFCC特征
        mfcc = librosa.feature.mfcc(
            y=audio,
            sr=self.sample_rate,
            n_mfcc=13,
            hop_length=self.hop_length,
            n_fft=self.n_fft
        )
        features['mfcc'] = mfcc
        
        # 基频
        f0, voiced_flag, _ = librosa.pyin(
            audio,
            fmin=librosa.note_to_hz('C2'),
            fmax=librosa.note_to_hz('C7'),
            sr=self.sample_rate,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )
        features['f0'] = f0
        features['voiced_flag'] = voiced_flag
        
        # 能量
        rms = librosa.feature.rms(
            y=audio,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )[0]
        features['rms'] = rms
        
        return features
    
    def _align_with_asr(self, text_info, audio_features, asr_result):
        """使用ASR结果进行对齐"""
        # 获取参考文本的字符
        ref_chars = text_info['chars']
        
        # 获取ASR的字符序列
        asr_chars = list(asr_result['text'].replace(' ', ''))
        
        # 使用DTW对齐字符序列
        char_alignment = self._dtw_align_chars(ref_chars, asr_chars)
        
        # 将字符对齐转换为时间对齐
        frame_rate = self.sample_rate / self.hop_length
        total_frames = audio_features['mfcc'].shape[1]
        duration = total_frames / frame_rate
        
        alignment = []
        for i, (ref_idx, asr_idx, score) in enumerate(char_alignment):
            if ref_idx is not None:
                # 计算时间边界
                char_start = i * duration / len(ref_chars)
                char_end = (i + 1) * duration / len(ref_chars)
                
                # 优化边界
                char_start, char_end = self._optimize_boundaries(
                    char_start, char_end, audio_features
                )
                
                alignment.append({
                    'char': ref_chars[ref_idx],
                    'pinyin': text_info['pinyin'][ref_idx] if ref_idx < len(text_info['pinyin']) else '',
                    'start': char_start,
                    'end': char_end,
                    'score': score,
                    'confidence': score
                })
        
        return alignment
    
    def _align_with_dtw(self, text_info, audio_features):
        """使用DTW直接对齐"""
        # 创建参考模板
        ref_template = self._create_reference_template(text_info)
        
        # 提取音频MFCC
        audio_mfcc = audio_features['mfcc'].T
        
        # 使用DTW对齐
        alignment = self._dtw_align_template(ref_template, audio_mfcc)
        
        return alignment
    
    def _create_reference_template(self, text_info):
        """创建参考模板"""
        # 这里应该使用标准发音创建模板
        # 简化版本：使用平均MFCC作为模板
        template_mfcc = []
        
        # 为每个字符创建一个简化的模板
        for i, char in enumerate(text_info['chars']):
            # 模拟每个字符的MFCC特征
            # 实际应用中应该使用标准发音的特征
            char_frames = max(10, int(np.random.normal(20, 5)))  # 每个字符约20帧
            char_mfcc = np.random.randn(char_frames, 13) * 0.1
            
            # 添加字符特定的特征
            if char in ['你', '我', '他']:
                char_mfcc[:, 0] += 0.5  # 第一个MFCC系数
            elif char in ['是', '的', '了']:
                char_mfcc[:, 1] += 0.3
            
            template_mfcc.append(char_mfcc)
        
        # 拼接所有字符
        template = np.vstack(template_mfcc)
        
        return template
    
    def _dtw_align_chars(self, ref_chars, asr_chars):
        """使用DTW对齐字符序列"""
        m, n = len(ref_chars), len(asr_chars)
        
        # 创建代价矩阵
        cost_matrix = np.zeros((m + 1, n + 1))
        cost_matrix[0, :] = np.inf
        cost_matrix[:, 0] = np.inf
        cost_matrix[0, 0] = 0
        
        # 填充代价矩阵
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                cost = 0 if ref_chars[i-1] == asr_chars[j-1] else 1
                cost_matrix[i, j] = cost + min(
                    cost_matrix[i-1, j],
                    cost_matrix[i, j-1],
                    cost_matrix[i-1, j-1]
                )
        
        # 回溯路径
        path = []
        i, j = m, n
        
        while i > 0 and j > 0:
            if cost_matrix[i, j] == cost_matrix[i-1, j-1] + (0 if ref_chars[i-1] == asr_chars[j-1] else 1):
                path.append((i-1, j-1))
                i -= 1
                j -= 1
            elif cost_matrix[i, j] == cost_matrix[i-1, j] + 1:
                path.append((i-1, None))
                i -= 1
            else:
                path.append((None, j-1))
                j -= 1
        
        # 处理剩余的
        while i > 0:
            path.append((i-1, None))
            i -= 1
        while j > 0:
            path.append((None, j-1))
            j -= 1
        
        # 反转路径并计算分数
        path.reverse()
        alignment = []
        for ref_idx, asr_idx in path:
            score = 1.0 if ref_idx is not None and asr_idx is not None and ref_chars[ref_idx] == asr_chars[asr_idx] else 0.0
            alignment.append((ref_idx, asr_idx, score))
        
        return alignment
    
    def _dtw_align_template(self, template, audio_mfcc):
        """使用DTW对齐模板和音频"""
        # 计算DTW距离
        dist, cost, acc, path = dtw(
            template, audio_mfcc,
            dist=lambda x, y: np.linalg.norm(x - y)
        )
        
        # 将路径转换为时间对齐
        frame_rate = self.sample_rate / self.hop_length
        alignment = []
        
        # 按字符分组
        chars_per_frame = len(template) / template.shape[0]
        
        for i in range(len(path[0])):
            template_idx = path[0][i]
            audio_idx = path[1][i]
            
            # 计算字符索引
            char_idx = int(template_idx * chars_per_frame)
            
            # 计算时间
            time = audio_idx / frame_rate
            
            if i == 0 or char_idx != alignment[-1]['char_idx'] if alignment else True:
                alignment.append({
                    'char_idx': char_idx,
                    'start_time': time,
                    'end_time': time,
                    'frames': [audio_idx]
                })
            else:
                alignment[-1]['end_time'] = time
                alignment[-1]['frames'].append(audio_idx)
        
        # 转换为最终格式
        result = []
        for i, align in enumerate(alignment):
            result.append({
                'char': '',  # 需要从text_info获取
                'pinyin': '',
                'start': align['start_time'],
                'end': align['end_time'],
                'score': 1.0 - (dist / len(path[0])),
                'confidence': 0.8
            })
        
        return result
    
    def _optimize_boundaries(self, start, end, audio_features):
        """优化时间边界"""
        # 使用能量和基频信息优化边界
        rms = audio_features['rms']
        f0 = audio_features['f0']
        voiced_flag = audio_features['voiced_flag']
        
        frame_rate = self.sample_rate / self.hop_length
        
        # 转换为帧索引
        start_frame = int(start * frame_rate)
        end_frame = int(end * frame_rate)
        
        # 向前查找能量上升点
        while start_frame > 0:
            if rms[start_frame] > rms[start_frame - 1] * 1.2:
                break
            start_frame -= 1
        
        # 向后查找能量下降点
        while end_frame < len(rms) - 1:
            if rms[end_frame] > rms[end_frame + 1] * 1.2:
                break
            end_frame += 1
        
        # 转换回时间
        start = start_frame / frame_rate
        end = end_frame / frame_rate
        
        return start, end
    
    def _refine_alignment(self, alignment, audio):
        """后处理：优化对齐结果"""
        # 确保时间连续
        for i in range(1, len(alignment)):
            if alignment[i]['start'] < alignment[i-1]['end']:
                alignment[i]['start'] = alignment[i-1]['end']
        
        # 确保不超出音频长度
        audio_duration = len(audio) / self.sample_rate
        for align in alignment:
            if align['end'] > audio_duration:
                align['end'] = audio_duration
        
        return alignment
    
    def extract_phone_alignment(self, alignment):
        """提取音素级别对齐"""
        phone_alignment = []
        
        for align in alignment:
            # 分离声母和韵母的时间
            char_duration = align['end'] - align['start']
            initial_ratio = 0.3  # 声母占30%时间
            final_ratio = 0.7    # 韵母占70%时间
            
            phone_alignment.append({
                'char': align['char'],
                'pinyin': align['pinyin'],
                'initial': {
                    'start': align['start'],
                    'end': align['start'] + char_duration * initial_ratio,
                    'score': align['score']
                },
                'final': {
                    'start': align['start'] + char_duration * initial_ratio,
                    'end': align['end'],
                    'score': align['score']
                },
                'tone': {
                    'start': align['start'],
                    'end': align['end'],
                    'score': align['score']
                }
            })
        
        return phone_alignment