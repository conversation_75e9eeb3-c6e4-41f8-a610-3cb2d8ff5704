import os
import json
import librosa
import numpy as np
from pypinyin import pinyin, Style
import jieba
from datetime import datetime
from app.services.audio_processor import AudioProcessor
from app.services.asr_service import ASRService
from app.services.alignment import ForceAligner
from app.services.evaluator import PronunciationEvaluator
from app.models import StandardAudio
from app import db

def evaluate_pronunciation(audio_path, text_content):
    """
    评估发音质量
    
    Args:
        audio_path: 音频文件路径
        text_content: 文本内容
        
    Returns:
        dict: 评估结果
    """
    try:
        # 1. 初始化服务
        audio_processor = AudioProcessor()
        asr_service = ASRService()
        aligner = ForceAligner()
        evaluator = PronunciationEvaluator()
        
        # 2. 音频预处理
        audio_result = audio_processor.process_audio(audio_path)
        user_audio = audio_result['audio']
        sr = audio_result['sample_rate']
        
        # 3. 文本预处理
        text_info = preprocess_text(text_content)
        
        # 4. 语音识别
        asr_result = asr_service.transcribe_with_pinyin(user_audio, sr)
        
        # 5. 强制对齐
        alignment_result = aligner.align_audio_text(user_audio, text_content, asr_result)
        
        # 6. 查找标准音频（可选）
        standard_audio_path = find_standard_audio(text_content)
        standard_audio = None
        if standard_audio_path:
            standard_result = audio_processor.process_audio(standard_audio_path)
            standard_audio = standard_result['audio']
        
        # 7. 多维度评分
        if standard_audio is not None:
            # 有标准音频时进行对比评估
            evaluation = evaluator.evaluate_pronunciation(
                user_audio, standard_audio, alignment_result
            )
        else:
            # 无标准音频时进行自评估
            evaluation = evaluate_without_standard(user_audio, alignment_result, evaluator)
        
        # 8. 生成可视化数据
        visualization_data = generate_visualization_data(
            text_content, alignment_result, evaluation['errors'], evaluation['scores']
        )
        
        # 9. 提取音频特征用于可视化
        waveform_data = user_audio.tolist()[:1000]  # 采样前1000个点
        pitch_curve = extract_pitch_curve(user_audio, sr).tolist()[:500]  # 采样前500个点
        
        return {
            'overall_score': evaluation['scores']['overall'],
            'dimension_scores': {
                'initial': evaluation['scores']['initial'],
                'final': evaluation['scores']['final'],
                'tone': evaluation['scores']['tone'],
                'fluency': evaluation['scores']['fluency']
            },
            'text_with_errors': visualization_data['text_with_errors'],
            'audio_analysis': {
                'duration': len(user_audio) / sr,
                'sample_rate': sr,
                'speech_rate': len(text_info['chars']) / (len(user_audio) / sr) * 60,  # 字/分钟
                'waveform_data': waveform_data,
                'pitch_curve': pitch_curve
            },
            'error_summary': visualization_data['error_summary'],
            'evaluation_details': evaluation['details'],
            'evaluation_time': datetime.now().isoformat()
        }
        
    except Exception as e:
        raise Exception(f"评估失败: {str(e)}")

def find_standard_audio(text_content):
    """查找匹配的标准音频"""
    # 简化版本：根据文本长度匹配
    text_length = len(text_content)
    
    # 查找长度最接近的标准音频
    standard_audios = StandardAudio.query.all()
    best_match = None
    min_diff = float('inf')
    
    for audio in standard_audios:
        diff = abs(len(audio.text_content) - text_length)
        if diff < min_diff:
            min_diff = diff
            best_match = audio
    
    if best_match and min_diff < 50:  # 长度差异小于50个字符
        return os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            'data', 'standard_audio', best_match.filename
        )
    
    return None

def evaluate_without_standard(user_audio, alignment_result, evaluator):
    """无标准音频时的评估"""
    # 基于统计模型的自评估
    scores = {
        'initial': 85.0,
        'final': 85.0,
        'tone': 85.0,
        'fluency': 85.0
    }
    
    # 基于对齐质量调整分数
    if alignment_result:
        confidence_scores = [align.get('confidence', 0.8) for align in alignment_result]
        avg_confidence = np.mean(confidence_scores)
        
        # 根据置信度调整分数
        for key in scores:
            scores[key] = 70 + avg_confidence * 30
    
    # 计算总分
    overall_score = sum(scores[dim] * evaluator.weights[dim] for dim in scores)
    scores['overall'] = round(overall_score, 1)
    
    # 检测错误
    errors = evaluator._detect_errors(scores, alignment_result)
    
    return {
        'scores': scores,
        'errors': errors,
        'details': evaluator._generate_evaluation_details(scores, alignment_result)
    }

def preprocess_audio(audio_path):
    """音频预处理"""
    # 加载音频
    audio, sr = librosa.load(audio_path, sr=16000)
    
    # 去除静音段
    audio = remove_silence(audio, sr)
    
    # 标准化音量
    audio = librosa.util.normalize(audio)
    
    return audio, sr

def remove_silence(audio, sr, top_db=20):
    """去除静音段"""
    # 检测非静音段
    intervals = librosa.effects.split(audio, top_db=top_db)
    
    # 拼接非静音段
    voiced_audio = []
    for interval in intervals:
        start, end = interval
        voiced_audio.extend(audio[start:end])
    
    return np.array(voiced_audio)

def preprocess_text(text_content):
    """文本预处理"""
    # 分词
    words = jieba.lcut(text_content)
    
    # 获取字符列表
    chars = list(text_content.replace(' ', ''))
    
    # 转换为拼音
    pinyin_list = pinyin(text_content, style=Style.TONE3)
    pinyin_flat = [p[0] for p in pinyin_list if p[0]]
    
    # 分离声母韵母
    phonemes = []
    for py in pinyin_flat:
        if py.isdigit():  # 带声调
            phoneme_info = separate_initial_final(py)
            phonemes.append(phoneme_info)
    
    return {
        'text': text_content,
        'words': words,
        'chars': chars,
        'pinyin': pinyin_flat,
        'phonemes': phonemes
    }

def separate_initial_final(pinyin_with_tone):
    """分离声母韵母"""
    # 去除声调数字
    pinyin_base = ''.join([c for c in pinyin_with_tone if not c.isdigit()])
    tone = int([c for c in pinyin_with_tone if c.isdigit()][0]) if any(c.isdigit() for c in pinyin_with_tone) else 0
    
    # 声母列表
    initials = ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 
                'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's', 'y', 'w']
    
    # 查找声母
    initial = ''
    final = pinyin_base
    
    for i in range(len(pinyin_base)):
        if pinyin_base[:i+1] in initials:
            initial = pinyin_base[:i+1]
            final = pinyin_base[i+1:]
    
    return {
        'initial': initial,
        'final': final,
        'tone': tone,
        'pinyin': pinyin_with_tone
    }

def perform_asr(audio, sr):
    """语音识别（暂时返回模拟数据）"""
    # 这里应该集成真正的ASR模型
    # 暂时返回模拟的时间戳
    duration = len(audio) / sr
    return {
        'segments': [
            {'start': i * 0.5, 'end': (i + 1) * 0.5, 'confidence': 0.9}
            for i in range(int(duration / 0.5))
        ]
    }

def perform_alignment(text_info, asr_result):
    """强制对齐（暂时返回模拟数据）"""
    chars = text_info['chars']
    phonemes = text_info['phonemes']
    
    # 模拟对齐结果
    alignment = []
    duration_per_char = 0.3  # 假设每个字符0.3秒
    
    for i, (char, phoneme) in enumerate(zip(chars, phonemes)):
        alignment.append({
            'char': char,
            'pinyin': phoneme['pinyin'],
            'start': i * duration_per_char,
            'end': (i + 1) * duration_per_char,
            'score': 0.8 + np.random.random() * 0.2  # 模拟分数
        })
    
    return alignment

def calculate_scores(audio, sr, text_info, alignment_result):
    """计算多维度分数"""
    # 模拟各维度分数
    initial_score = 85 + np.random.randint(-10, 10)
    final_score = 80 + np.random.randint(-15, 15)
    tone_score = 75 + np.random.randint(-20, 20)
    fluency_score = 90 + np.random.randint(-10, 10)
    
    overall_score = (initial_score * 0.3 + 
                    final_score * 0.3 + 
                    tone_score * 0.3 + 
                    fluency_score * 0.1)
    
    return {
        'overall': round(overall_score, 1),
        'initial': round(initial_score, 1),
        'final': round(final_score, 1),
        'tone': round(tone_score, 1),
        'fluency': round(fluency_score, 1)
    }

def detect_errors(text_info, alignment_result, scores):
    """检测错误"""
    errors = []
    
    # 模拟错误检测
    for i, alignment in enumerate(alignment_result):
        if alignment['score'] < 0.7:
            error_type = np.random.choice(['initial', 'final', 'tone'])
            errors.append({
                'index': i,
                'char': alignment['char'],
                'error_type': error_type,
                'description': f'{alignment["char"]}的{error_type}发音不准确'
            })
    
    return errors

def extract_pitch_curve(audio, sr):
    """提取基频曲线"""
    # 使用pyin提取基频
    f0, voiced_flag, voiced_probs = librosa.pyin(
        audio, 
        fmin=librosa.note_to_hz('C2'), 
        fmax=librosa.note_to_hz('C7'),
        sr=sr
    )
    
    # 填充未 voiced 的部分
    f0[~voiced_flag] = 0
    
    return f0

def generate_visualization_data(text_content, alignment_result, errors, scores):
    """生成可视化数据"""
    # 生成带错误标注的文本
    text_with_errors = []
    error_dict = {e['index']: e for e in errors}
    
    for i, alignment in enumerate(alignment_result):
        error = error_dict.get(i)
        text_with_errors.append({
            'char': alignment['char'],
            'pinyin': alignment['pinyin'],
            'start': alignment['start'],
            'end': alignment['end'],
            'score': alignment['score'],
            'error_type': error['error_type'] if error else None,
            'error_detail': error['description'] if error else None
        })
    
    # 错误统计
    error_summary = {
        'total_errors': len(errors),
        'initial_errors': len([e for e in errors if e['error_type'] == 'initial']),
        'final_errors': len([e for e in errors if e['error_type'] == 'final']),
        'tone_errors': len([e for e in errors if e['error_type'] == 'tone']),
        'accuracy': round((1 - len(errors) / len(alignment_result)) * 100, 1)
    }
    
    return {
        'text_with_errors': text_with_errors,
        'error_summary': error_summary
    }