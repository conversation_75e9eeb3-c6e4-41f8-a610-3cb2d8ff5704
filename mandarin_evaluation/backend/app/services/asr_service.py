import os
import json
import numpy as np
from transformers import AutoProcessor, AutoModelForCTC
import torch
import librosa
from pypinyin import pinyin, Style
import jieba

class ASRService:
    """语音识别服务"""
    
    def __init__(self, model_name="wenet/wenet-chinese-base"):
        """
        初始化ASR模型
        
        Args:
            model_name: 模型名称
        """
        self.model_name = model_name
        self.model = None
        self.processor = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._load_model()
    
    def _load_model(self):
        """加载ASR模型"""
        try:
            # 使用transformers库加载预训练模型
            self.processor = AutoProcessor.from_pretrained(self.model_name)
            self.model = AutoModelForCTC.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            
        except Exception as e:
            print(f"加载模型失败: {str(e)}")
            print("将使用模拟ASR功能")
            self.model = None
            self.processor = None
    
    def transcribe(self, audio, sample_rate=16000):
        """
        语音转文字
        
        Args:
            audio: 音频数据
            sample_rate: 采样率
            
        Returns:
            dict: 识别结果
        """
        if self.model is None:
            # 使用模拟数据
            return self._mock_transcribe(audio, sample_rate)
        
        try:
            # 重采样到模型需要的采样率
            if sample_rate != 16000:
                audio = librosa.resample(audio, orig_sr=sample_rate, target_sr=16000)
            
            # 预处理音频
            inputs = self.processor(
                audio, 
                sampling_rate=16000, 
                return_tensors="pt", 
                padding=True
            ).to(self.device)
            
            # 推理
            with torch.no_grad():
                logits = self.model(**inputs).logits
            
            # 解码
            predicted_ids = torch.argmax(logits, dim=-1)
            transcription = self.processor.batch_decode(predicted_ids)[0]
            
            # 获取时间戳（简化版本）
            duration = len(audio) / 16000
            segments = self._create_segments(transcription, duration)
            
            return {
                'text': transcription,
                'segments': segments,
                'confidence': 0.95  # 模拟置信度
            }
            
        except Exception as e:
            print(f"ASR识别失败: {str(e)}")
            return self._mock_transcribe(audio, sample_rate)
    
    def _mock_transcribe(self, audio, sample_rate):
        """模拟ASR识别（用于测试）"""
        duration = len(audio) / sample_rate
        
        # 模拟识别文本（这里应该返回实际的识别结果）
        mock_text = "这是一段模拟的语音识别结果"
        
        # 创建模拟的时间段
        segments = []
        segment_duration = 2.0  # 每段2秒
        num_segments = int(duration / segment_duration)
        
        for i in range(num_segments):
            segments.append({
                'start': i * segment_duration,
                'end': (i + 1) * segment_duration,
                'text': mock_text[i % len(mock_text)],
                'confidence': 0.8 + np.random.random() * 0.2
            })
        
        return {
            'text': mock_text,
            'segments': segments,
            'confidence': 0.85
        }
    
    def _create_segments(self, text, duration):
        """创建时间段（简化版本）"""
        # 分词
        words = jieba.lcut(text)
        
        # 平均分配时间
        if len(words) == 0:
            return []
        
        segment_duration = duration / len(words)
        segments = []
        
        for i, word in enumerate(words):
            segments.append({
                'start': i * segment_duration,
                'end': (i + 1) * segment_duration,
                'text': word,
                'confidence': 0.9
            })
        
        return segments
    
    def transcribe_with_pinyin(self, audio, sample_rate=16000):
        """
        语音转文字并转换为拼音
        
        Args:
            audio: 音频数据
            sample_rate: 采样率
            
        Returns:
            dict: 带拼音的识别结果
        """
        # 获取识别结果
        result = self.transcribe(audio, sample_rate)
        
        # 转换为拼音
        pinyin_list = pinyin(result['text'], style=Style.TONE3)
        pinyin_flat = [p[0] for p in pinyin_list if p[0]]
        
        # 获取字符列表
        chars = list(result['text'].replace(' ', ''))
        
        # 创建字符到拼音的映射
        char_pinyin_map = []
        pinyin_index = 0
        
        for char in chars:
            if pinyin_index < len(pinyin_flat):
                # 检查拼音是否对应字符
                if self._pinyin_to_char(pinyin_flat[pinyin_index]) == char:
                    char_pinyin_map.append({
                        'char': char,
                        'pinyin': pinyin_flat[pinyin_index]
                    })
                    pinyin_index += 1
                else:
                    # 多音字处理
                    char_pinyin_map.append({
                        'char': char,
                        'pinyin': pinyin_flat[pinyin_index]
                    })
                    pinyin_index += 1
            else:
                char_pinyin_map.append({
                    'char': char,
                    'pinyin': ''
                })
        
        return {
            'text': result['text'],
            'pinyin': pinyin_flat,
            'chars': chars,
            'char_pinyin_map': char_pinyin_map,
            'segments': result['segments'],
            'confidence': result['confidence']
        }
    
    def _pinyin_to_char(self, pinyin):
        """拼音转字符（简化版本）"""
        # 这里应该有一个完整的拼音到字符的映射
        # 简化处理，返回拼音的第一个字符
        tone = ''.join([c for c in pinyin if c.isdigit()])
        base_pinyin = pinyin.rstrip('12345')
        
        # 这里只是一个示例，实际应该使用完整的映射
        if base_pinyin == 'ni':
            return '你'
        elif base_pinyin == 'hao':
            return '好'
        elif base_pinyin == 'ma':
            return '吗'
        else:
            return base_pinyin[0] if base_pinyin else ''
    
    def align_with_text(self, asr_result, reference_text):
        """
        将ASR结果与参考文本对齐
        
        Args:
            asr_result: ASR识别结果
            reference_text: 参考文本
            
        Returns:
            dict: 对齐结果
        """
        # 获取参考文本的字符和拼音
        ref_chars = list(reference_text.replace(' ', ''))
        ref_pinyin = pinyin(reference_text, style=Style.TONE3)
        ref_pinyin_flat = [p[0] for p in ref_pinyin if p[0]]
        
        # 获取ASR结果的字符
        asr_chars = list(asr_result['text'].replace(' ', ''))
        
        # 使用动态规划进行对齐
        alignment = self._dynamic_time_warping(ref_chars, asr_chars)
        
        # 创建对齐结果
        aligned_result = []
        duration = asr_result['segments'][-1]['end'] if asr_result['segments'] else 0
        
        for i, (ref_idx, asr_idx, score) in enumerate(alignment):
            if ref_idx is not None and ref_idx < len(ref_chars):
                # 计算时间
                char_duration = duration / len(ref_chars)
                start_time = ref_idx * char_duration
                end_time = (ref_idx + 1) * char_duration
                
                aligned_result.append({
                    'char': ref_chars[ref_idx],
                    'pinyin': ref_pinyin_flat[ref_idx] if ref_idx < len(ref_pinyin_flat) else '',
                    'start': start_time,
                    'end': end_time,
                    'score': score,
                    'matched': asr_idx is not None
                })
        
        return aligned_result
    
    def _dynamic_time_warping(self, ref_chars, asr_chars):
        """动态时间规整算法"""
        # 创建距离矩阵
        m, n = len(ref_chars), len(asr_chars)
        dtw = np.zeros((m + 1, n + 1))
        
        # 初始化
        dtw[0, 0] = 0
        for i in range(1, m + 1):
            dtw[i, 0] = float('inf')
        for j in range(1, n + 1):
            dtw[0, j] = float('inf')
        
        # 填充距离矩阵
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                cost = 0 if ref_chars[i-1] == asr_chars[j-1] else 1
                dtw[i, j] = cost + min(dtw[i-1, j], dtw[i, j-1], dtw[i-1, j-1])
        
        # 回溯路径
        alignment = []
        i, j = m, n
        
        while i > 0 or j > 0:
            if i == 0:
                j -= 1
                alignment.append((None, j, 0))
            elif j == 0:
                i -= 1
                alignment.append((i-1, None, 0))
            else:
                min_val = min(dtw[i-1, j], dtw[i, j-1], dtw[i-1, j-1])
                if min_val == dtw[i-1, j-1]:
                    i -= 1
                    j -= 1
                    score = 1 - dtw[i+1, j+1]
                    alignment.append((i, j, score))
                elif min_val == dtw[i-1, j]:
                    i -= 1
                    alignment.append((i, None, 0))
                else:
                    j -= 1
                    alignment.append((None, j, 0))
        
        # 反转路径
        alignment.reverse()
        
        return alignment