import os
import librosa
import numpy as np
from app.utils.audio_utils import (
    load_audio, extract_mfcc, extract_pitch, extract_formants,
    detect_silence, normalize_audio
)

class AudioProcessor:
    """音频预处理器"""
    
    def __init__(self, sample_rate=16000):
        self.sample_rate = sample_rate
        self.n_mfcc = 13
        self.hop_length = 512
        self.n_fft = 2048
    
    def process_audio(self, file_path):
        """
        处理音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            dict: 处理结果
        """
        try:
            # 1. 加载音频
            audio, sr = load_audio(file_path, sr=self.sample_rate)
            
            # 2. 预处理
            processed_audio = self._preprocess(audio)
            
            # 3. 提取特征
            features = self._extract_features(processed_audio)
            
            # 4. 检测语音活动
            vad_result = self._voice_activity_detection(processed_audio)
            
            return {
                'audio': processed_audio,
                'sample_rate': sr,
                'duration': len(processed_audio) / sr,
                'features': features,
                'vad': vad_result
            }
            
        except Exception as e:
            raise Exception(f"音频处理失败: {str(e)}")
    
    def _preprocess(self, audio):
        """音频预处理"""
        # 1. 归一化
        audio = normalize_audio(audio)
        
        # 2. 去除直流偏移
        audio = audio - np.mean(audio)
        
        # 3. 去除静音段
        non_silent_intervals = librosa.effects.split(
            audio,
            top_db=20,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )
        
        if len(non_silent_intervals) > 0:
            # 拼接非静音段
            processed_audio = []
            for start, end in non_silent_intervals:
                processed_audio.extend(audio[start:end])
            processed_audio = np.array(processed_audio)
        else:
            processed_audio = audio
        
        return processed_audio
    
    def _extract_features(self, audio):
        """提取音频特征"""
        features = {}
        
        # 1. MFCC特征
        mfcc = extract_mfcc(
            audio,
            sr=self.sample_rate,
            n_mfcc=self.n_mfcc,
            hop_length=self.hop_length
        )
        features['mfcc'] = mfcc
        
        # 2. 一阶差分
        mfcc_delta = librosa.feature.delta(mfcc)
        features['mfcc_delta'] = mfcc_delta
        
        # 3. 二阶差分
        mfcc_delta2 = librosa.feature.delta(mfcc, order=2)
        features['mfcc_delta2'] = mfcc_delta2
        
        # 4. 基频(F0)
        f0, voiced_flag = extract_pitch(audio, self.sample_rate)
        features['f0'] = f0
        features['voiced_flag'] = voiced_flag
        
        # 5. 共振峰
        formants = extract_formants(audio, self.sample_rate)
        features['formants'] = formants
        
        # 6. 能量
        rms = librosa.feature.rms(
            y=audio,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )[0]
        features['rms'] = rms
        
        # 7. 过零率
        zcr = librosa.feature.zero_crossing_rate(
            audio,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )[0]
        features['zcr'] = zcr
        
        return features
    
    def _voice_activity_detection(self, audio):
        """语音活动检测"""
        # 检测静音段
        silence_intervals = detect_silence(
            audio,
            sr=self.sample_rate,
            top_db=20,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )
        
        # 计算语音比例
        total_duration = len(audio) / self.sample_rate
        silence_duration = sum(end - start for start, end in silence_intervals)
        speech_ratio = 1 - (silence_duration / total_duration)
        
        return {
            'silence_intervals': silence_intervals,
            'speech_ratio': speech_ratio,
            'total_duration': total_duration,
            'speech_duration': total_duration - silence_duration
        }
    
    def extract_segment_features(self, audio, start_time, end_time):
        """
        提取指定时间段的特征
        
        Args:
            audio: 音频数据
            start_time: 开始时间(秒)
            end_time: 结束时间(秒)
            
        Returns:
            dict: 段特征
        """
        # 转换为采样点
        start_sample = int(start_time * self.sample_rate)
        end_sample = int(end_time * self.sample_rate)
        
        # 提取片段
        segment = audio[start_sample:end_sample]
        
        # 确保片段长度足够
        if len(segment) < self.n_fft:
            # 零填充
            segment = np.pad(segment, (0, self.n_fft - len(segment)))
        
        # 提取特征
        features = self._extract_features(segment)
        
        return features
    
    def get_audio_info(self, file_path):
        """
        获取音频文件基本信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            dict: 音频信息
        """
        try:
            info = librosa.get_info(filename=file_path)
            return {
                'filename': os.path.basename(file_path),
                'format': info.format,
                'duration': info.duration,
                'sample_rate': info.sample_rate,
                'channels': info.channels,
                'bit_rate': getattr(info, 'bit_rate', 0)
            }
        except Exception as e:
            raise Exception(f"获取音频信息失败: {str(e)}")