import numpy as np
import librosa
from scipy import signal
from scipy.spatial.distance import euclidean
from sklearn.preprocessing import StandardScaler
import math

class PronunciationEvaluator:
    """发音评估器"""
    
    def __init__(self, sample_rate=16000):
        self.sample_rate = sample_rate
        self.hop_length = 512
        self.n_fft = 2048
        self.scaler = StandardScaler()
        
        # 定义评分权重
        self.weights = {
            'initial': 0.3,
            'final': 0.3,
            'tone': 0.3,
            'fluency': 0.1
        }
        
        # 定义错误阈值
        self.thresholds = {
            'initial': 0.7,
            'final': 0.7,
            'tone': 0.6,
            'fluency': 0.8
        }
    
    def evaluate_pronunciation(self, user_audio, standard_audio, text_alignment):
        """
        评估发音质量
        
        Args:
            user_audio: 用户音频
            standard_audio: 标准音频
            text_alignment: 文本对齐结果
            
        Returns:
            dict: 评估结果
        """
        # 提取特征
        user_features = self._extract_comprehensive_features(user_audio)
        standard_features = self._extract_comprehensive_features(standard_audio)
        
        # 多维度评分
        scores = {
            'initial': self._evaluate_initials(user_features, standard_features, text_alignment),
            'final': self._evaluate_finals(user_features, standard_features, text_alignment),
            'tone': self._evaluate_tones(user_features, standard_features, text_alignment),
            'fluency': self._evaluate_fluency(user_features, text_alignment)
        }
        
        # 计算总分
        overall_score = sum(scores[dim] * self.weights[dim] for dim in scores)
        scores['overall'] = round(overall_score, 1)
        
        # 四舍五入
        for dim in ['initial', 'final', 'tone', 'fluency']:
            scores[dim] = round(scores[dim], 1)
        
        # 检测错误
        errors = self._detect_errors(scores, text_alignment)
        
        return {
            'scores': scores,
            'errors': errors,
            'details': self._generate_evaluation_details(scores, text_alignment)
        }
    
    def _extract_comprehensive_features(self, audio):
        """提取综合特征"""
        features = {}
        
        # 1. MFCC特征
        mfcc = librosa.feature.mfcc(
            y=audio,
            sr=self.sample_rate,
            n_mfcc=13,
            hop_length=self.hop_length,
            n_fft=self.n_fft
        )
        features['mfcc'] = mfcc
        features['mfcc_delta'] = librosa.feature.delta(mfcc)
        features['mfcc_delta2'] = librosa.feature.delta(mfcc, order=2)
        
        # 2. 基频特征
        f0, voiced_flag, voiced_probs = librosa.pyin(
            audio,
            fmin=librosa.note_to_hz('C2'),
            fmax=librosa.note_to_hz('C7'),
            sr=self.sample_rate,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )
        features['f0'] = f0
        features['voiced_flag'] = voiced_flag
        features['voiced_probs'] = voiced_probs
        
        # 3. 共振峰
        formants = self._extract_formants(audio)
        features['formants'] = formants
        
        # 4. 能量特征
        rms = librosa.feature.rms(
            y=audio,
            frame_length=self.n_fft,
            hop_length=self.hop_length
        )[0]
        features['rms'] = rms
        
        # 5. 频谱质心
        spectral_centroid = librosa.feature.spectral_centroid(
            y=audio,
            sr=self.sample_rate,
            hop_length=self.hop_length
        )[0]
        features['spectral_centroid'] = spectral_centroid
        
        # 6. 频谱带宽
        spectral_bandwidth = librosa.feature.spectral_bandwidth(
            y=audio,
            sr=self.sample_rate,
            hop_length=self.hop_length
        )[0]
        features['spectral_bandwidth'] = spectral_bandwidth
        
        return features
    
    def _extract_formants(self, audio):
        """提取共振峰"""
        formants = []
        
        # 分帧处理
        for i in range(0, len(audio) - self.n_fft, self.hop_length):
            frame = audio[i:i + self.n_fft]
            
            # 预加重
            frame = np.append(frame[0], frame[1:] - 0.97 * frame[:-1])
            
            # 加窗
            window = np.hamming(len(frame))
            frame = frame * window
            
            # LPC
            lpc_order = 2 + int(self.sample_rate / 1000)
            try:
                A = librosa.lpc(frame, order=lpc_order)
                roots = np.roots(A)
                roots = roots[np.abs(roots) < 1]
                angles = np.angle(roots)
                freqs = sorted(angles * (self.sample_rate / (2 * np.pi)))
                freqs = [f for f in freqs if 0 < f < self.sample_rate / 2]
                
                if len(freqs) >= 4:
                    formants.append(freqs[:4])
                else:
                    formants.append(freqs + [0] * (4 - len(freqs)))
            except:
                formants.append([0] * 4)
        
        return np.array(formants)
    
    def _evaluate_initials(self, user_features, standard_features, alignment):
        """评估声母发音"""
        if not alignment:
            return 85.0
        
        initial_scores = []
        
        for align in alignment:
            if 'initial' in align and align['initial']:
                # 提取声母时间段
                start_frame = int(align['initial']['start'] * self.sample_rate / self.hop_length)
                end_frame = int(align['initial']['end'] * self.sample_rate / self.hop_length)
                
                if end_frame > start_frame:
                    # 提取MFCC特征
                    user_mfcc = user_features['mfcc'][:, start_frame:end_frame]
                    standard_mfcc = standard_features['mfcc'][:, start_frame:end_frame]
                    
                    if user_mfcc.size > 0 and standard_mfcc.size > 0:
                        # 计算动态时间规整距离
                        distance = self._calculate_dtw_distance(user_mfcc.T, standard_mfcc.T)
                        score = max(0, 100 - distance * 50)
                        initial_scores.append(score)
        
        return np.mean(initial_scores) if initial_scores else 85.0
    
    def _evaluate_finals(self, user_features, standard_features, alignment):
        """评估韵母发音"""
        if not alignment:
            return 85.0
        
        final_scores = []
        
        for align in alignment:
            if 'final' in align and align['final']:
                # 提取韵母时间段
                start_frame = int(align['final']['start'] * self.sample_rate / self.hop_length)
                end_frame = int(align['final']['end'] * self.sample_rate / self.hop_length)
                
                if end_frame > start_frame:
                    # 提取共振峰特征
                    user_formants = user_features['formants'][start_frame:end_frame]
                    standard_formants = standard_features['formants'][start_frame:end_frame]
                    
                    if len(user_formants) > 0 and len(standard_formants) > 0:
                        # 计算前三个共振峰的距离
                        f1_dist = np.mean(np.abs(user_formants[:, 0] - standard_formants[:, 0]))
                        f2_dist = np.mean(np.abs(user_formants[:, 1] - standard_formants[:, 1]))
                        f3_dist = np.mean(np.abs(user_formants[:, 2] - standard_formants[:, 2]))
                        
                        # 综合评分
                        avg_dist = (f1_dist + f2_dist + f3_dist) / 3
                        score = max(0, 100 - avg_dist * 2)
                        final_scores.append(score)
        
        return np.mean(final_scores) if final_scores else 85.0
    
    def _evaluate_tones(self, user_features, standard_features, alignment):
        """评估声调发音"""
        if not alignment:
            return 85.0
        
        tone_scores = []
        
        for align in alignment:
            if 'tone' in align and align['tone']:
                # 提取声调时间段
                start_frame = int(align['tone']['start'] * self.sample_rate / self.hop_length)
                end_frame = int(align['tone']['end'] * self.sample_rate / self.hop_length)
                
                if end_frame > start_frame:
                    # 提取基频
                    user_f0 = user_features['f0'][start_frame:end_frame]
                    standard_f0 = standard_features['f0'][start_frame:end_frame]
                    
                    # 过滤无效值
                    valid_mask = (user_f0 > 0) & (standard_f0 > 0)
                    if np.sum(valid_mask) > 0:
                        user_f0_valid = user_f0[valid_mask]
                        standard_f0_valid = standard_f0[valid_mask]
                        
                        # 归一化
                        user_f0_norm = user_f0_valid / np.mean(user_f0_valid)
                        standard_f0_norm = standard_f0_valid / np.mean(standard_f0_valid)
                        
                        # 计算轮廓相似度
                        correlation = np.corrcoef(user_f0_norm, standard_f0_norm)[0, 1]
                        if not np.isnan(correlation):
                            score = 50 + correlation * 50
                            tone_scores.append(score)
        
        return np.mean(tone_scores) if tone_scores else 85.0
    
    def _evaluate_fluency(self, user_features, alignment):
        """评估流畅性"""
        if not alignment:
            return 85.0
        
        fluency_score = 100
        
        # 1. 语速评估
        total_duration = alignment[-1]['end'] if alignment else 0
        char_count = len(alignment)
        if total_duration > 0:
            speech_rate = char_count / total_duration * 60  # 字/分钟
            # 理想语速：180-240字/分钟
            if speech_rate < 120:
                fluency_score -= 10
            elif speech_rate > 300:
                fluency_score -= 10
        
        # 2. 停顿评估
        pause_count = 0
        total_pause_duration = 0
        for i in range(1, len(alignment)):
            gap = alignment[i]['start'] - alignment[i-1]['end']
            if gap > 0.1:  # 大于100ms的停顿
                pause_count += 1
                total_pause_duration += gap
        
        # 停顿次数过多扣分
        if pause_count > char_count * 0.1:
            fluency_score -= min(20, (pause_count - char_count * 0.1) * 2)
        
        # 3. 能量稳定性
        rms = user_features['rms']
        if len(rms) > 0:
            rms_std = np.std(rms) / np.mean(rms)
            if rms_std > 0.5:  # 能量波动太大
                fluency_score -= min(10, (rms_std - 0.5) * 20)
        
        return max(0, fluency_score)
    
    def _calculate_dtw_distance(self, seq1, seq2):
        """计算DTW距离"""
        from dtw import dtw
        
        # 确保输入是二维数组
        if len(seq1.shape) == 1:
            seq1 = seq1.reshape(-1, 1)
        if len(seq2.shape) == 1:
            seq2 = seq2.reshape(-1, 1)
        
        # 计算DTW
        dist = dtw(seq1, seq2, dist=lambda x, y: np.linalg.norm(x - y))
        
        # 归一化距离
        normalized_dist = dist.distance / (len(seq1) + len(seq2))
        
        return normalized_dist
    
    def _detect_errors(self, scores, alignment):
        """检测发音错误"""
        errors = []
        
        for i, align in enumerate(alignment):
            char_errors = []
            
            # 声母错误
            if scores['initial'] < self.thresholds['initial']:
                char_errors.append({
                    'type': 'initial',
                    'description': '声母发音不准确'
                })
            
            # 韵母错误
            if scores['final'] < self.thresholds['final']:
                char_errors.append({
                    'type': 'final',
                    'description': '韵母发音不准确'
                })
            
            # 声调错误
            if scores['tone'] < self.thresholds['tone']:
                char_errors.append({
                    'type': 'tone',
                    'description': '声调不准确'
                })
            
            if char_errors:
                errors.append({
                    'index': i,
                    'char': align.get('char', ''),
                    'errors': char_errors
                })
        
        return errors
    
    def _generate_evaluation_details(self, scores, alignment):
        """生成评估详情"""
        details = {
            'level': self._get_score_level(scores['overall']),
            'suggestions': self._generate_suggestions(scores),
            'statistics': {
                'total_chars': len(alignment),
                'duration': alignment[-1]['end'] if alignment else 0,
                'speech_rate': len(alignment) / (alignment[-1]['end'] if alignment else 1) * 60
            }
        }
        
        return details
    
    def _get_score_level(self, score):
        """获取分数等级"""
        if score >= 90:
            return '优秀'
        elif score >= 80:
            return '良好'
        elif score >= 70:
            return '中等'
        elif score >= 60:
            return '及格'
        else:
            return '需改进'
    
    def _generate_suggestions(self, scores):
        """生成改进建议"""
        suggestions = []
        
        if scores['initial'] < 80:
            suggestions.append('注意声母的发音部位和发音方法')
        
        if scores['final'] < 80:
            suggestions.append('注意韵母的口型和舌位')
        
        if scores['tone'] < 80:
            suggestions.append('注意声调的起伏变化')
        
        if scores['fluency'] < 80:
            suggestions.append('控制语速，减少不必要的停顿')
        
        return suggestions