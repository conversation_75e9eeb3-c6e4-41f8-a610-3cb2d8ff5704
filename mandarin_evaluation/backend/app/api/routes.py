import os
import uuid
from flask import request, jsonify, current_app
from flask_cors import cross_origin
from app.api import bp
from app.models import EvaluationTask, StandardAudio
from app import db
from app.services.evaluation_service import evaluate_pronunciation

# 延迟导入Celery以避免循环导入
def make_celery(app):
    from celery import Celery
    
    celery = Celery(
        app.import_name,
        backend=app.config['CELERY_RESULT_BACKEND'],
        broker=app.config['CELERY_BROKER_URL']
    )
    celery.conf.update(app.config)
    
    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery

# Celery实例将在需要时创建
celery = None

@bp.route('/upload', methods=['POST'])
@cross_origin()
def upload_file():
    """上传音频文件和文本"""
    try:
        if 'audio' not in request.files:
            return jsonify({'error': '没有音频文件'}), 400
        
        audio_file = request.files['audio']
        text_content = request.form.get('text', '')
        
        if audio_file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not text_content:
            return jsonify({'error': '文本内容不能为空'}), 400
        
        # 检查文件类型
        if not allowed_file(audio_file.filename):
            return jsonify({'error': '文件类型不支持，请上传MP3、WAV或M4A文件'}), 400
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 保存文件
        filename = f"{task_id}_{audio_file.filename}"
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        audio_file.save(filepath)
        
        # 创建任务记录
        task = EvaluationTask(
            task_id=task_id,
            filename=filename,
            text_content=text_content,
            status='pending'
        )
        db.session.add(task)
        db.session.commit()
        
        # 异步处理评估任务
        global celery
        if celery is None:
            celery = make_celery(current_app)
        evaluate_task.delay(task_id, filepath, text_content)
        
        return jsonify({
            'task_id': task_id,
            'message': '文件上传成功，正在处理中'
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/tasks/<task_id>', methods=['GET'])
@cross_origin()
def get_task_status(task_id):
    """获取任务状态"""
    task = EvaluationTask.query.filter_by(task_id=task_id).first()
    
    if not task:
        return jsonify({'error': '任务不存在'}), 404
    
    return jsonify(task.to_dict())

@bp.route('/tasks/<task_id>/result', methods=['GET'])
@cross_origin()
def get_task_result(task_id):
    """获取评估结果"""
    task = EvaluationTask.query.filter_by(task_id=task_id).first()
    
    if not task:
        return jsonify({'error': '任务不存在'}), 404
    
    if task.status != 'completed':
        return jsonify({'error': '任务未完成'}), 400
    
    return jsonify(task.result)

@bp.route('/standard-audios', methods=['GET'])
@cross_origin()
def get_standard_audios():
    """获取标准音频列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    audios = StandardAudio.query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'items': [audio.to_dict() for audio in audios.items],
        'total': audios.total,
        'pages': audios.pages,
        'current_page': page
    })

@celery.task
def evaluate_task(task_id, audio_path, text_content):
    """异步评估任务"""
    try:
        # 更新任务状态
        task = EvaluationTask.query.filter_by(task_id=task_id).first()
        if not task:
            return
        
        task.status = 'processing'
        task.progress = 10
        db.session.commit()
        
        # 执行评估
        result = evaluate_pronunciation(audio_path, text_content)
        
        # 更新任务结果
        task.status = 'completed'
        task.progress = 100
        task.result = result
        db.session.commit()
        
    except Exception as e:
        # 更新任务错误状态
        task = EvaluationTask.query.filter_by(task_id=task_id).first()
        if task:
            task.status = 'failed'
            task.error_message = str(e)
            db.session.commit()

def allowed_file(filename):
    """检查文件类型"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']