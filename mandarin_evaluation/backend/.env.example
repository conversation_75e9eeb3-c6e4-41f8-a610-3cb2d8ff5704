# 环境变量配置示例
# 复制此文件为 .env 并修改相应值

# Flask配置
SECRET_KEY=your-secret-key-here
FLASK_ENV=development

# 数据库配置
DATABASE_URL=sqlite:///app.db
# DATABASE_URL=postgresql://username:password@localhost/mandarin_eval

# Redis配置（用于Celery）
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 文件上传配置
MAX_CONTENT_LENGTH=104857600  # 100MB in bytes

# 跨域配置
CORS_ORIGINS=http://localhost:3000